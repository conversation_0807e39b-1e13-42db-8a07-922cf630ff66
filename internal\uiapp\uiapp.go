package uiapp

import (
	"context"
	"encoding/json"
	"fmt"
	"sync/atomic"
	"time"

	"Golang/SystemSupport/internal/config"

	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// App: đố<PERSON> tượ<PERSON> bind sang UI (được Wails tạo wailsjs để gọi từ React)
type App struct {
	ctx context.Context
}

func New() *App { return &App{} }

// Startup được Wails gọi 1 lần khi app khởi động
func (a *App) Startup(ctx context.Context) {
	a.ctx = ctx
	globalCtx.Store(ctx) // lưu để tiện Emit từ package khác

	// Emit MQTT info sau khi startup
	go func() {
		time.Sleep(2 * time.Second) // Đợi UI sẵn sàng
		mqttInfoJson := a.GetMQTTInfo()
		runtime.EventsEmit(ctx, "mqttinfo", mqttInfoJson)
	}()
}

// Hàm public để UI gọi (hoặc backend khác gọi thông qua binding)
func (a *App) PushStatus(s string) {
	runtime.EventsEmit(a.ctx, "status", s)
}

// Test function để emit serverlog từ UI
func (a *App) TestServerLog(message string) {
	runtime.EventsEmit(a.ctx, "serverlog", message)
}

// TestMQTT: test method đơn giản
func (a *App) TestMQTT() string {
	return "MQTT test OK"
}

// MQTTInfo: struct chứa thông tin MQTT để trả về UI
type MQTTInfo struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	ClientID string `json:"client_id"`
	Status   string `json:"status"`
}

// GetMQTTInfo: lấy thông tin MQTT từ config để hiển thị trên UI
func (a *App) GetMQTTInfo() string {
	// Load config từ file
	cfg, err := config.Load("../../config.json")
	if err != nil {
		// Fallback: thử load từ current directory
		cfg, err = config.Load("config.json")
		if err != nil {
			info := MQTTInfo{
				Host:     "localhost",
				Port:     1883,
				ClientID: "go-systemsupport",
				Status:   fmt.Sprintf("Config error: %v", err),
			}
			jsonData, _ := json.Marshal(info)
			return string(jsonData)
		}
	}

	var info MQTTInfo
	if cfg.MQTT == nil {
		info = MQTTInfo{
			Host:     "localhost",
			Port:     1883,
			ClientID: "go-systemsupport",
			Status:   "No config",
		}
	} else {
		info = MQTTInfo{
			Host:     cfg.MQTT.Host,
			Port:     cfg.MQTT.Port,
			ClientID: cfg.MQTT.ClientID,
			Status:   "Configured",
		}
	}

	jsonData, _ := json.Marshal(info)
	return string(jsonData)
}

// Trả về options cho wails.Run
func (a *App) Options() *options.App {
	return &options.App{
		Title:     "SystemSupport",
		OnStartup: a.Startup,
		Bind:      []interface{}{a},
	}
}

// --------- Helpers để emit event từ mọi nơi trong code base ---------

var globalCtx atomic.Value // holds context.Context

// EmitStatus: có thể gọi từ bất kỳ package nào sau khi app đã Startup
func EmitStatus(s string) {
	if v := globalCtx.Load(); v != nil {
		runtime.EventsEmit(v.(context.Context), "status", s)
	}
}
