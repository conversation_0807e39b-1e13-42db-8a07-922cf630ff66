package main

import (
	"bytes"
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	srt "runtime" // alias để dùng NumCPU mà không nhầm với wails runtime
	"strings"
	"sync"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"

	"Golang/SystemSupport/internal/config"
	"Golang/SystemSupport/internal/httpserver"
	"Golang/SystemSupport/internal/mqttclient"
	"Golang/SystemSupport/internal/sign"
	"Golang/SystemSupport/internal/store/influx"
	"Golang/SystemSupport/internal/supportsvc"
	"Golang/SystemSupport/internal/uiapp"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"
	"github.com/wailsapp/wails/v2/pkg/options/windows"
	wruntime "github.com/wailsapp/wails/v2/pkg/runtime"
)

// ===== Querier adapter (giữ nguyên) =====
// Adapter này dùng để "bọc" Influx.Querier gốc
// vì supportsvc.DatabaseHandler cần interface cụ thể.
type querierAdapter struct{ q influx.Querier }

// Query: gọi vào Influx Query thực tế
func (qa *querierAdapter) Query(ctx context.Context, org, bucket, flux string) ([]map[string]any, error) {
	return qa.q.Query(ctx, org, flux)
}

// Close: đóng kết nối Influx
func (qa *querierAdapter) Close() error { qa.q.Close(); return nil }

// ===== Toàn cục để quản lý vòng đời server =====

var (
	httpSrv    *http.Server        // server HTTPS chính
	mqttHandle *mqttclient.Handle  // quản lý MQTT connection
	svc        *supportsvc.Service // service xử lý nghiệp vụ qua MQTT
	influxQ    influx.Querier      // kết nối truy vấn Influx

	wctx      context.Context    // context của Wails (UI)
	cancelSrv context.CancelFunc // để hủy server context
	uiApp     = uiapp.New()      // instance ứng dụng UI (bind sang frontend)

	// Log buffer để lưu logs trước khi UI sẵn sàng
	logBuffer     []string
	mqttLogBuffer []string
	logMutex      sync.Mutex
)

// addToLogBuffer: thêm log vào buffer tạm
func addToLogBuffer(msg string) {
	logMutex.Lock()
	defer logMutex.Unlock()

	if isMQTTLog(msg) {
		mqttLogBuffer = append(mqttLogBuffer, msg)
		// Giới hạn tối đa 500 MQTT logs
		if len(mqttLogBuffer) > 500 {
			mqttLogBuffer = mqttLogBuffer[1:]
		}
	} else {
		logBuffer = append(logBuffer, msg)
		// Giới hạn tối đa 1000 system logs
		if len(logBuffer) > 1000 {
			logBuffer = logBuffer[1:]
		}
	}
}

// flushLogBuffer: gửi toàn bộ log trong buffer sang UI qua sự kiện "serverlog"
func flushLogBuffer() {
	logMutex.Lock()
	defer logMutex.Unlock()

	if wctx == nil {
		return
	}
	fmt.Fprintf(os.Stderr, "[DEBUG] %s\n", logBuffer)

	// Phân loại lại và emit system logs
	go func() {
		for i, logMsg := range logBuffer {
			// Kiểm tra lại xem có phải MQTT log không
			if isMQTTLog(logMsg) {
				// Nếu là MQTT log, emit đến mqttlog
				wruntime.EventsEmit(wctx, "mqttlog", logMsg)
			} else {
				// Nếu không, emit đến serverlog
				wruntime.EventsEmit(wctx, "serverlog", logMsg)
			}
			// Delay nhỏ giữa các events để frontend có thể xử lý
			if i < len(logBuffer)-1 {
				time.Sleep(10 * time.Millisecond)
			}
		}
	}()

	// Emit MQTT logs từ buffer riêng
	go func() {
		for i, mqttMsg := range mqttLogBuffer {
			wruntime.EventsEmit(wctx, "mqttlog", mqttMsg)
			// Delay nhỏ giữa các events để frontend có thể xử lý
			if i < len(mqttLogBuffer)-1 {
				time.Sleep(10 * time.Millisecond)
			}
		}
	}()

	// Xóa buffer sau khi flush
	logBuffer = nil
	mqttLogBuffer = nil
}

// logBufferWriter: writer custom để redirect log Go sang buffer và stderr
type logBufferWriter struct{}

// Write: được gọi mỗi khi log.Print... ghi log
func (w *logBufferWriter) Write(p []byte) (int, error) {
	// Ghi ra stderr như mặc định
	fmt.Fprint(os.Stderr, string(p))

	// Lưu bản copy vào buffer (loại bỏ newline)
	msg := strings.TrimRight(string(p), "\n\r")
	if msg != "" {
		addToLogBuffer(msg)
	}

	return len(p), nil
}

// setupLogBuffering: cấu hình lại hệ thống log của Go để đi qua logBufferWriter
func setupLogBuffering() {
	writer := &logBufferWriter{}
	log.SetOutput(writer)
	log.SetFlags(log.LstdFlags | log.Lmicroseconds) // thêm timestamp + microsecond
}

// setupRealTimeUILogging: setup real-time UI logging sau khi flush buffer
func setupRealTimeUILogging(wctx context.Context) {
	// Tạo writer emit trực tiếp đến UI
	realTimeWriter := &realTimeUIWriter{
		emit: func(line string) {
			if wctx != nil {
				wruntime.EventsEmit(wctx, "serverlog", line)
			}
		},
	}

	// Kiểm tra dev mode để quyết định output
	isDevMode := checkDevMode()

	if isDevMode {
		// Dev mode: output đến cả stderr và UI
		mw := io.MultiWriter(os.Stderr, realTimeWriter)
		log.SetOutput(mw)
	} else {
		// Production mode: chỉ output đến UI
		log.SetOutput(realTimeWriter)
	}

	log.SetFlags(log.LstdFlags | log.Lmicroseconds)
}

// realTimeUIWriter: writer emit logs trực tiếp đến UI
type realTimeUIWriter struct {
	mu   sync.Mutex
	buf  []byte
	emit func(line string)
}

// isMQTTLog: kiểm tra xem log có phải là MQTT log không
func isMQTTLog(line string) bool {
	// fmt.Fprintf(os.Stderr, "[DEBUG] %s\n", line)
	mqttKeywords := []string{
		"mqtt",
		"[MQTT]",
		"broker",
		"client_id=",
		"tcp://",
		"retained=",
		"qos=",
	}

	lineLower := strings.ToLower(line)
	for _, keyword := range mqttKeywords {
		if strings.Contains(lineLower, strings.ToLower(keyword)) {
			return true
		}
	}
	return false
}

func (w *realTimeUIWriter) Write(p []byte) (int, error) {
	w.mu.Lock()
	defer w.mu.Unlock()

	w.buf = append(w.buf, p...)

	// Emit complete lines ngay lập tức
	for {
		i := bytes.IndexByte(w.buf, '\n')
		if i < 0 {
			break
		}
		line := strings.TrimRight(string(w.buf[:i]), "\r")

		// Phân loại và emit logs
		if strings.TrimSpace(line) != "" {
			if isMQTTLog(line) {
				// Emit đến MQTT logs
				if wctx != nil {
					wruntime.EventsEmit(wctx, "mqttlog", line)
				} else {
					// Fallback: thêm vào buffer nếu chưa có context
					logMutex.Lock()
					mqttLogBuffer = append(mqttLogBuffer, line)
					if len(mqttLogBuffer) > 500 {
						mqttLogBuffer = mqttLogBuffer[1:]
					}
					logMutex.Unlock()
				}
			} else {
				// Emit đến system logs
				if w.emit != nil {
					w.emit(line)
				}
			}
		}

		w.buf = w.buf[i+1:]
	}

	return len(p), nil
}

// checkDevMode: kiểm tra có phải dev mode không
func checkDevMode() bool {
	// Kiểm tra environment variables của Wails dev
	if os.Getenv("WAILS_DEV") == "true" ||
		os.Getenv("WAILS_DEVSERVER") != "" ||
		os.Getenv("FRONTEND_DEVSERVER_URL") != "" {
		return true
	}

	// Kiểm tra executable name có chứa "-dev" không
	if len(os.Args) > 0 {
		execName := os.Args[0]
		if strings.Contains(execName, "-dev.exe") || strings.Contains(execName, "-dev") {
			return true
		}
	}

	// Kiểm tra có DevServer URL không (chỉ có trong dev mode)
	for _, arg := range os.Args {
		if strings.Contains(arg, "localhost:") && strings.Contains(arg, "http://") {
			return true
		}
	}

	// Kiểm tra working directory có chứa frontend/src không
	if _, err := os.Stat("frontend/src"); err == nil {
		// Có frontend/src nhưng không có frontend/dist = dev mode
		if _, err := os.Stat("frontend/dist"); os.IsNotExist(err) {
			return true
		}
	}

	return false
}

// loadConfigSafe: load file config.json, fallback nếu không có
func loadConfigSafe(wctx context.Context) *config.Config {
	paths := []string{
		"config.json",
		"../../config.json", // fallback: ở repo root
	}
	for _, p := range paths {
		if cfg, err := config.Load(p); err == nil && cfg != nil {
			log.Printf("Loaded config from %s", p)
			return cfg
		} else if err != nil {
			log.Printf("config load warning from %s: %v", p, err)
		}
	}
	log.Printf("No config.json found. Running with defaults.")
	return &config.Config{} // mặc định rỗng → MQTT nil, Influx tắt
}

// ===== Khởi động server (srvCtx cho lifecycle, wctx cho UI log/event) =====

func runServer(wctx, srvCtx context.Context) error {
	// 1) Load config
	log.Printf("[SERVER] Loading config...")
	cfg := loadConfigSafe(wctx)
	log.Printf("[SERVER] Config loaded")

	// 2) Tạo signer (Ed25519) → phục vụ ký dữ liệu
	log.Printf("[SERVER] Initializing signer...")
	signer, err := sign.NewEd25519SignerFromPEM(sign.DefaultEd25519PrivPEM)
	if err != nil {
		return fmt.Errorf("init signer error: %w", err)
	}
	log.Printf("[SERVER] Building handler...")
	handler := httpserver.BuildHandler(signer) // build router HTTP
	log.Printf("[SERVER] Loading TLS config...")
	tlsCfg := httpserver.TLSConfigFromEmbedded() // TLS tự sinh
	log.Printf("[SERVER] TLS config loaded")

	// 3) MQTT
	if cfg.MQTT != nil && cfg.MQTT.Host != "" && cfg.MQTT.Port != 0 {
		mqttHandle, err = mqttclient.Start(srvCtx, cfg.MQTT)
		if err != nil {
			return fmt.Errorf("mqtt start error: %w", err)
		}
	} else {
		log.Printf("MQTT disabled or not configured")
	}

	// 4) Influx (nếu bật trong config)
	if cfg.Influx.Enable {
		influxQ = influx.NewV2(influx.V2Config{
			URL: cfg.Influx.URL, Token: cfg.Influx.Token, Org: cfg.Influx.Org,
		})
	}

	// 5) HTTPS server
	addr := "127.0.0.1:8443"
	ln, err := tls.Listen("tcp", addr, tlsCfg)
	if err != nil {
		return fmt.Errorf("listen tls: %w", err)
	}
	httpSrv = &http.Server{
		Handler:   handler,
		TLSConfig: tlsCfg,
		ErrorLog:  log.Default(), // log lỗi HTTP cũng đi qua UI
	}
	log.Printf("Go server on https://%s", addr)
	go func() {
		if err := httpSrv.Serve(ln); err != nil && err != http.ErrServerClosed {
			log.Printf("http serve: %v", err)
			wruntime.EventsEmit(wctx, "status", "Server error: "+err.Error())
		}
	}()

	// 6) Support service + handler (khi có MQTT)
	if mqttHandle != nil {
		var mqttCli mqtt.Client
		if c := mqttHandle.Client(); c != nil {
			mqttCli = c
		} else {
			return fmt.Errorf("mqtt client is nil")
		}

		handlers := []supportsvc.Handler{}
		if influxQ != nil {
			// Tạo DatabaseHandler cho truy vấn Influx qua MQTT
			adapter := &querierAdapter{q: influxQ}
			dbHandler := &supportsvc.DatabaseHandler{
				MQTT:          mqttCli,
				QoS:           0,
				Retain:        false,
				DefaultOrg:    cfg.Influx.Org,
				DefaultBucket: cfg.Influx.DefaultBucket,
				Querier:       adapter,
				ScriptTimeout: 0, // không giới hạn
				MaxRows:       0, // không cắt dữ liệu
				Writer: &supportsvc.HTTPLineWriter{
					URL:   cfg.Influx.URL,
					Token: cfg.Influx.Token,
				},
			}
			handlers = append(handlers, dbHandler)
		} else {
			log.Printf("Influx disabled — DatabaseHandler not registered")
		}

		// Khởi tạo service chính
		svc = supportsvc.New(mqttCli, supportsvc.Config{
			TopicSupport:  cfg.MQTT.TopicSupport,
			QoS:           0,
			Concurrency:   srt.NumCPU(), // xử lý song song theo số CPU
			PublishRetain: false,
		}, handlers...)

		if err := svc.Start(srvCtx); err != nil {
			return fmt.Errorf("support service start error: %w", err)
		}
	}

	// Báo cho UI biết server đã chạy
	wruntime.EventsEmit(wctx, "status", "Server is running")
	return nil
}

// ===== Shutdown gọn =====

func stopServer() {
	// Tắt HTTP server
	if httpSrv != nil {
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		_ = httpSrv.Shutdown(shutdownCtx)
		cancel()
	}
	// Chờ service dừng hẳn
	if svc != nil {
		svc.Wait()
	}
	// Đóng Influx
	if influxQ != nil {
		influxQ.Close()
	}
	// Đóng MQTT
	if mqttHandle != nil {
		mqttHandle.Close()
	}
	// Báo UI
	if wctx != nil {
		wruntime.EventsEmit(wctx, "status", "Server is stopping")
	}
}

// ===== main: chạy Wails + host server =====

func main() {
	// Setup log buffering cho cả dev và production để capture startup logs
	setupLogBuffering()

	if err := wails.Run(&options.App{
		Title:  "SystemSupport",
		Width:  1200,
		Height: 800,
		AssetServer: &assetserver.Options{
			Assets: assets, // build-in assets từ assets.go
		},
		Windows: &windows.Options{
			WebviewIsTransparent: false,
			WindowIsTranslucent:  false,
			DisableWindowIcon:    false,
			Messages: &windows.Messages{
				InstallationRequired: "Ứng dụng này cần WebView2 runtime. Bạn có muốn tải về không?",
				UpdateRequired:       "Cần cập nhật WebView2 runtime. Vui lòng cập nhật để tiếp tục.",
				FailedToInstall:      "Không thể cài đặt WebView2 runtime. Vui lòng liên hệ hỗ trợ.",
			},
		},
		OnStartup: func(ctx context.Context) {
			// Lưu context Wails để emit event cho UI
			wctx = ctx
			uiApp.Startup(ctx)

			log.Printf("[BOOT] Wails started successfully")

			// Tạo context con cho server (theo vòng đời app)
			srvCtx, cancel := context.WithCancel(ctx)
			cancelSrv = cancel

			// Kiểm tra mode và setup UI logging phù hợp
			isDevMode := checkDevMode()

			if isDevMode {
				// Dev mode: flush buffer với delay dài hơn + real-time logging
				go func() {
					time.Sleep(1500 * time.Millisecond) // Delay dài hơn để frontend hoàn toàn sẵn sàng

					// Flush buffered logs sau test
					flushLogBuffer()

					// Setup real-time logging sau flush
					setupRealTimeUILogging(wctx)

					log.Printf("[BOOT] UI log bridge activated")
					log.Printf("[INFO] Real-time logging is now active")

					// Emit lại startup logs để đảm bảo frontend nhận được
					time.Sleep(500 * time.Millisecond)
					log.Printf("[INFO] Re-emitting startup logs for UI...")

					// Emit lại từng startup log
					go func() {
						startupLogs := []string{
							// "[MAIN] Starting SystemSupport SCADA (DEV MODE)",
							// "[WebView2] Environment created successfully",
							// "[BOOT] Wails started successfully",
							// "[SERVER] Loading config...",
							// "Loaded config from ../../config.json",
							// "[SERVER] Config loaded",
							// "[SERVER] Initializing signer...",
							// "[SERVER] Building handler...",
							// "[SERVER] Loading TLS config...",
							// "[SERVER] TLS config loaded",
							// "MQTT connected -> tcp://localhost:1883 (client_id=go-systemsupport)",
							// "Go server on https://127.0.0.1:8443",
							// "MQTT status \"SystemSupportStatus\" -> \"online\" (retained=true, qos=0)",
							// "[supportsvc] subscribed \"SystemSupport\" (qos=0, workers=24)",
						}

						for _, logMsg := range startupLogs {
							wruntime.EventsEmit(wctx, "serverlog", logMsg)
							time.Sleep(50 * time.Millisecond)
						}
					}()

					// Test logs để confirm real-time logging
					time.Sleep(3 * time.Second)

					// Periodic test logs mỗi 5 giây
					ticker := time.NewTicker(5 * time.Second)
					defer ticker.Stop()

					// counter := 1
					// for {
					// 	select {
					// 	case <-ticker.C:
					// 		log.Printf("[TEST] Periodic log #%d - real-time logging active", counter)
					// 		// Thêm test MQTT log mỗi 3 lần
					// 		if counter%3 == 0 {
					// 			log.Printf("MQTT status test #%d - connection active", counter/3)
					// 		}
					// 		counter++
					// 	case <-srvCtx.Done():
					// 		return
					// 	}
					// }
				}()
			} else {
				// Production mode: flush buffer sau delay dài hơn
				go func() {
					time.Sleep(2 * time.Second) // Delay dài hơn cho production

					// Flush buffered logs
					flushLogBuffer()

					log.Printf("[BOOT] UI log bridge activated")
					log.Printf("[INFO] All previous logs have been sent to UI")

					// Emit lại startup logs để đảm bảo frontend nhận được
					time.Sleep(500 * time.Millisecond)
					log.Printf("[INFO] Re-emitting startup logs for UI...")

					// Emit lại từng startup log
					go func() {
						startupLogs := []string{
							// "[MAIN] Starting SystemSupport SCADA (PRODUCTION)",
							// "[WebView2] Environment created successfully",
							// "[BOOT] Wails started successfully",
							// "[SERVER] Loading config...",
							// "Loaded config from ../../config.json",
							// "[SERVER] Config loaded",
							// "[SERVER] Initializing signer...",
							// "[SERVER] Building handler...",
							// "[SERVER] Loading TLS config...",
							// "[SERVER] TLS config loaded",
							// "MQTT connected -> tcp://localhost:1883 (client_id=go-systemsupport)",
							// "Go server on https://127.0.0.1:8443",
							// "MQTT status \"SystemSupportStatus\" -> \"online\" (retained=true, qos=0)",
							// "[supportsvc] subscribed \"SystemSupport\" (qos=0, workers=24)",
						}

						for _, logMsg := range startupLogs {
							wruntime.EventsEmit(wctx, "serverlog", logMsg)
							time.Sleep(50 * time.Millisecond)
						}
					}()

					// Setup real-time logging SAU KHI emit startup logs
					time.Sleep(2 * time.Second)
					setupRealTimeUILogging(wctx)
					log.Printf("[INFO] Real-time logging is now active")

					// Test logs để confirm real-time logging
					time.Sleep(3 * time.Second)

					// Periodic test logs mỗi 10 giây cho production
					ticker := time.NewTicker(10 * time.Second)
					defer ticker.Stop()

					// counter := 1
					// for {
					// 	select {
					// 	case <-ticker.C:
					// 		log.Printf("[TEST] Periodic log #%d - production logging active", counter)
					// 		counter++
					// 	case <-srvCtx.Done():
					// 		return
					// 	}
					// }
				}()
			}

			wruntime.EventsEmit(wctx, "status", "Server is running normally")

			// Khởi động server song song
			go func() {
				if err := runServer(wctx, srvCtx); err != nil {
					log.Printf("runServer error: %v", err)
					wruntime.EventsEmit(wctx, "status", "Server error: "+err.Error())
				}
			}()
		},
		OnShutdown: func(ctx context.Context) {
			// Hủy server context trước → các component sẽ nhận ctx.Done()
			if cancelSrv != nil {
				cancelSrv()
			}
			// Dừng server gọn gàng
			stopServer()
		},
		Bind: []interface{}{
			uiApp, // cho phép UI gọi các method của uiApp
		},
	}); err != nil {
		log.Printf("[MAIN] Wails.Run error: %v", err)
		log.Fatal(err)
	}
	log.Printf("[MAIN] Application exited normally")
}
