package main

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"os"
	"strings"
	"sync"
	"time"

	wruntime "github.com/wailsapp/wails/v2/pkg/runtime"
)

// uiLogSink triển khai io.Writer, gom theo dòng rồi emit "serverlog" lên UI.
type uiLogSink struct {
	mu     sync.Mutex
	buf    []byte
	emit   func(line string)
	buffer []string // buffer logs trước khi UI sẵn sàng
	ready  bool     // UI đã sẵn sàng chưa
}

func (s *uiLogSink) Write(p []byte) (int, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.buf = append(s.buf, p...)
	for {
		i := bytes.IndexByte(s.buf, '\n')
		if i < 0 {
			break
		}
		line := strings.TrimRight(string(s.buf[:i]), "\r")

		if s.ready && s.emit != nil {
			s.emit(line)
		} else {
			// Buffer logs nếu UI chưa sẵn sàng
			s.buffer = append(s.buffer, line)
			// Debug to stderr để tránh circular logging
			fmt.Fprintf(os.Stderr, "[DEBUG] Buffered log (total: %d): %s\n", len(s.buffer), line)
			// Giới hạn buffer để tránh memory leak
			if len(s.buffer) > 1000 {
				s.buffer = s.buffer[1:]
			}
		}
		s.buf = s.buf[i+1:]
	}
	return len(p), nil
}

// ActivateUI: kích hoạt UI và replay tất cả buffered logs
func (s *uiLogSink) ActivateUI() {
	s.mu.Lock()
	defer s.mu.Unlock()

	bufferCount := len(s.buffer)
	fmt.Fprintf(os.Stderr, "[DEBUG] ActivateUI called - buffer has %d logs\n", bufferCount)

	s.ready = true

	// Replay tất cả buffered logs
	if s.emit != nil {
		fmt.Fprintf(os.Stderr, "[DEBUG] Starting replay of %d logs\n", len(s.buffer))
		for i, line := range s.buffer {
			fmt.Fprintf(os.Stderr, "[DEBUG] Replaying log %d: %s\n", i+1, line)
			s.emit(line)
			// Small delay để đảm bảo events được process
			time.Sleep(10 * time.Millisecond)
		}
		fmt.Fprintf(os.Stderr, "[DEBUG] Finished replaying %d logs\n", len(s.buffer))
	}

	// Clear buffer sau khi replay
	s.buffer = nil
	fmt.Fprintf(os.Stderr, "[DEBUG] ActivateUI completed - replayed %d logs\n", bufferCount)
}

// setupUILogging: redirect log.Default() -> console + UI event "serverlog".
func setupUILogging(wctx context.Context) *uiLogSink {
	fmt.Fprintf(os.Stderr, "[DEBUG] setupUILogging called\n")

	sink := &uiLogSink{
		emit: func(line string) {
			// Có thể gọi từ goroutine khác miễn là dùng đúng Wails ctx
			fmt.Fprintf(os.Stderr, "[DEBUG] Emitting to UI: %s\n", line)
			if wctx == nil {
				fmt.Fprintf(os.Stderr, "[ERROR] wctx is nil, cannot emit event\n")
				return
			}
			wruntime.EventsEmit(wctx, "serverlog", line)
			fmt.Fprintf(os.Stderr, "[DEBUG] Event emitted successfully\n")
		},
		buffer: make([]string, 0),
		ready:  false,
	}

	// Ghi ra stderr + UI
	mw := io.MultiWriter(os.Stderr, sink)

	// Set log flags để có timestamp chi tiết
	log.SetFlags(log.LstdFlags | log.Lmicroseconds)
	log.SetOutput(mw)

	fmt.Fprintf(os.Stderr, "[DEBUG] setupUILogging completed\n")
	return sink
}

// setupUILoggingDirect: setup UI logging trực tiếp (không buffer)
func setupUILoggingDirect(wctx context.Context) {
	fmt.Fprintf(os.Stderr, "[DEBUG] setupUILoggingDirect called\n")

	// Tạo writer đơn giản emit trực tiếp
	uiWriter := &directUIWriter{
		emit: func(line string) {
			fmt.Fprintf(os.Stderr, "[DEBUG] Direct emit: %s\n", line)
			if wctx != nil {
				wruntime.EventsEmit(wctx, "serverlog", line)
			}
		},
	}

	// Redirect log output
	mw := io.MultiWriter(os.Stderr, uiWriter)
	log.SetFlags(log.LstdFlags | log.Lmicroseconds)
	log.SetOutput(mw)

	fmt.Fprintf(os.Stderr, "[DEBUG] setupUILoggingDirect completed\n")
}

// directUIWriter: writer đơn giản không buffer
type directUIWriter struct {
	mu   sync.Mutex
	buf  []byte
	emit func(line string)
}

func (w *directUIWriter) Write(p []byte) (int, error) {
	w.mu.Lock()
	defer w.mu.Unlock()

	w.buf = append(w.buf, p...)

	// Process complete lines
	for {
		i := bytes.IndexByte(w.buf, '\n')
		if i < 0 {
			break
		}
		line := strings.TrimRight(string(w.buf[:i]), "\r")
		if w.emit != nil {
			w.emit(line)
		}
		w.buf = w.buf[i+1:]
	}

	return len(p), nil
}

// setupSimpleUILogging: setup UI logging đơn giản nhất
func setupSimpleUILogging(wctx context.Context) {
	// Tạo writer emit trực tiếp mỗi dòng log
	writer := &simpleWriter{
		emit: func(line string) {
			if wctx != nil {
				wruntime.EventsEmit(wctx, "serverlog", line)
			}
		},
	}

	// Redirect log output
	mw := io.MultiWriter(os.Stderr, writer)
	log.SetOutput(mw)
}

// simpleWriter: writer đơn giản nhất
type simpleWriter struct {
	mu   sync.Mutex
	buf  []byte
	emit func(line string)
}

func (w *simpleWriter) Write(p []byte) (int, error) {
	w.mu.Lock()
	defer w.mu.Unlock()

	w.buf = append(w.buf, p...)

	// Emit complete lines
	for {
		i := bytes.IndexByte(w.buf, '\n')
		if i < 0 {
			break
		}
		line := strings.TrimRight(string(w.buf[:i]), "\r")
		if w.emit != nil {
			w.emit(line)
		}
		w.buf = w.buf[i+1:]
	}

	return len(p), nil
}

// activateUILoggingWithDelay: kích hoạt UI logging sau delay
func activateUILoggingWithDelay(sink *uiLogSink, delay time.Duration) {
	go func() {
		time.Sleep(delay)

		// Lấy số lượng buffered logs trước khi activate
		sink.mu.Lock()
		bufferCount := len(sink.buffer)
		sink.mu.Unlock()

		sink.ActivateUI()
		log.Printf("[BOOT] UI log bridge activated - replayed %d buffered logs", bufferCount)
	}()
}
